import { ContextManager } from "./ContextManager"
import { Anthropic } from "@anthropic-ai/sdk"
import { ClineMessage } from "@shared/ExtensionMessage"
import { ApiHandler } from "@api/index"

describe("ContextManager Improvements", () => {
	let contextManager: ContextManager
	
	beforeEach(() => {
		contextManager = new ContextManager()
	})

	describe("User Direct Input Preservation", () => {
		it("should preserve user direct inputs during truncation", () => {
			const apiMessages: Anthropic.Messages.MessageParam[] = [
				// First user-assistant pair (always preserved)
				{ role: "user", content: [{ type: "text", text: "Initial user message" }] },
				{ role: "assistant", content: [{ type: "text", text: "Initial assistant response" }] },
				
				// Tool call (can be truncated)
				{ role: "user", content: [{ type: "text", text: "[read_file for 'test.js'] Result:" }] },
				{ role: "assistant", content: [{ type: "text", text: "File content here" }] },
				
				// User direct input (should be preserved)
				{ role: "user", content: [{ type: "text", text: "Please modify the function to handle edge cases" }] },
				{ role: "assistant", content: [{ type: "text", text: "I'll modify the function" }] },
				
				// Another tool call (can be truncated)
				{ role: "user", content: [{ type: "text", text: "[write_to_file for 'test.js'] Result:" }] },
				{ role: "assistant", content: [{ type: "text", text: "File written successfully" }] },
				
				// Another user direct input (should be preserved)
				{ role: "user", content: [{ type: "text", text: "Now add unit tests for this function" }] },
				{ role: "assistant", content: [{ type: "text", text: "I'll add unit tests" }] },
			]

			const truncationRange = contextManager.getNextTruncationRange(
				apiMessages,
				undefined,
				"half"
			)

			// The truncation should avoid removing user direct inputs
			const [start, end] = truncationRange
			
			// Check that user direct input messages are not in the truncation range
			for (let i = start; i <= end; i++) {
				const message = apiMessages[i]
				if (message.role === "user") {
					const firstBlock = message.content[0] as any
					if (firstBlock.type === "text") {
						// Should not be a direct user input
						expect(firstBlock.text).toMatch(/^\[.*\] Result:$/)
					}
				}
			}
		})
	})

	describe("MD5-based File Duplicate Detection", () => {
		it("should detect duplicates based on content hash, not file path", () => {
			const apiMessages: Anthropic.Messages.MessageParam[] = [
				{ role: "user", content: [{ type: "text", text: "Initial message" }] },
				{ role: "assistant", content: [{ type: "text", text: "Initial response" }] },
				
				// First read of file with content A
				{ 
					role: "user", 
					content: [
						{ type: "text", text: "[read_file for 'test.js'] Result:" },
						{ type: "text", text: "function test() { return 'A'; }" }
					]
				},
				{ role: "assistant", content: [{ type: "text", text: "I see the function" }] },
				
				// Second read of same file with different content B
				{ 
					role: "user", 
					content: [
						{ type: "text", text: "[read_file for 'test.js'] Result:" },
						{ type: "text", text: "function test() { return 'B'; }" }
					]
				},
				{ role: "assistant", content: [{ type: "text", text: "The function changed" }] },
				
				// Third read of same file with content A again (should be detected as duplicate)
				{ 
					role: "user", 
					content: [
						{ type: "text", text: "[read_file for 'test.js'] Result:" },
						{ type: "text", text: "function test() { return 'A'; }" }
					]
				},
				{ role: "assistant", content: [{ type: "text", text: "Back to original" }] },
			]

			// This test would verify that the third read (same content as first) 
			// is detected as duplicate and replaced with a notice
			// while the second read (different content) is kept
		})

		it("should handle file mentions with content-based deduplication", () => {
			const apiMessages: Anthropic.Messages.MessageParam[] = [
				{ role: "user", content: [{ type: "text", text: "Initial message" }] },
				{ role: "assistant", content: [{ type: "text", text: "Initial response" }] },
				
				// File mention with content A
				{ 
					role: "user", 
					content: [
						{ type: "text", text: "Please review this:" },
						{ type: "text", text: '<file_content path="test.js">function test() { return "A"; }</file_content>' }
					]
				},
				{ role: "assistant", content: [{ type: "text", text: "Reviewed the function" }] },
				
				// Same file path but different content B
				{ 
					role: "user", 
					content: [
						{ type: "text", text: "Now check this version:" },
						{ type: "text", text: '<file_content path="test.js">function test() { return "B"; }</file_content>' }
					]
				},
				{ role: "assistant", content: [{ type: "text", text: "This version is different" }] },
				
				// Same content A again (should be detected as duplicate)
				{ 
					role: "user", 
					content: [
						{ type: "text", text: "Back to original:" },
						{ type: "text", text: '<file_content path="test.js">function test() { return "A"; }</file_content>' }
					]
				},
				{ role: "assistant", content: [{ type: "text", text: "Back to the first version" }] },
			]

			// This test would verify content-based deduplication for file mentions
		})
	})

	describe("Integration Tests", () => {
		it("should preserve user inputs and deduplicate by content hash together", async () => {
			// Test that both improvements work together correctly
			const apiMessages: Anthropic.Messages.MessageParam[] = [
				{ role: "user", content: [{ type: "text", text: "Start task" }] },
				{ role: "assistant", content: [{ type: "text", text: "Starting" }] },
				
				// Tool call with file content A
				{ 
					role: "user", 
					content: [
						{ type: "text", text: "[read_file for 'test.js'] Result:" },
						{ type: "text", text: "const x = 1;" }
					]
				},
				{ role: "assistant", content: [{ type: "text", text: "Read file" }] },
				
				// User direct input (should be preserved)
				{ role: "user", content: [{ type: "text", text: "Make this more robust" }] },
				{ role: "assistant", content: [{ type: "text", text: "I'll improve it" }] },
				
				// Tool call with same file content A (should be deduplicated)
				{ 
					role: "user", 
					content: [
						{ type: "text", text: "[read_file for 'test.js'] Result:" },
						{ type: "text", text: "const x = 1;" }
					]
				},
				{ role: "assistant", content: [{ type: "text", text: "Read file again" }] },
				
				// Another user direct input (should be preserved)
				{ role: "user", content: [{ type: "text", text: "Add error handling" }] },
				{ role: "assistant", content: [{ type: "text", text: "Adding error handling" }] },
			]

			// Test the integration of both features
			const clineMessages: ClineMessage[] = []
			const mockApi = {} as ApiHandler
			
			const result = await contextManager.getNewContextMessagesAndMetadata(
				apiMessages,
				clineMessages,
				mockApi,
				undefined,
				-1,
				"/tmp/test"
			)

			// Verify that user direct inputs are preserved and duplicates are handled
			expect(result.truncatedConversationHistory).toBeDefined()
		})
	})
})

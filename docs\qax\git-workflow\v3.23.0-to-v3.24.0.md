# v3.23.0 到 v3.24.0 升级日志

## 基本信息
- **更新日期**: 2025-08-14
- **源版本**: v3.23.0
- **目标版本**: v3.24.0
- **变更统计**: 183 个文件变更，新增 4,795 行，删除 5,736 行

## 变更统计

### 目标版本已删除或重命名文件列表
#### 删除的文件 (27 个)
- **.changeset/yellow-meals-smash.md** - 变更集配置文件
- **src/core/assistant-message/diff-json.ts** - 差异 JSON 处理
- **src/core/controller/file/subscribeToWorkspaceUpdates.ts** - 工作区更新订阅
- **src/core/prompts/model_prompts/claude4-experimental.ts** - Claude 4 实验性提示
- **src/core/prompts/model_prompts/jsonToolToXml.ts** - JSON 工具转 XML
- **src/core/storage/state.ts** - 状态存储核心文件
- **src/integrations/workspace/WorkspaceTracker.ts** - 工作区跟踪器
- **src/hosts/vscode/diagnostics.ts** - VSCode 诊断功能
- **src/hosts/vscode/hostbridge/diff/closeDiff.ts** - 关闭差异视图
- **webview-ui/src/components/chat/chat-view/components/messages/StreamingIndicator.tsx** - 流式指示器组件
- **webview-ui/src/components/chat/chat-view/hooks/useButtonState.ts** - 按钮状态钩子
- **所有 src/core/tools/ 目录下的工具文件** (14 个) - 工具系统重构

#### 重命名的文件 (1 个)
- **src/core/mentions/__tests__/index.test.ts** → **src/core/mentions/index.test.ts** - 测试文件重新组织

### 关键文件 `package.json` 变更内容
```json
{
  "name": "claude-dev",
  "displayName": "Cline",
  "description": "Autonomous coding agent right in your IDE, capable of creating/editing files, running commands, using the browser, and more with your permission every step of the way.",
- "version": "3.23.0",
+ "version": "3.24.0",
  // 测试脚本更新，添加 --allow-package-secrets sendgrid 参数
- "test:e2e": "playwright install && vsce package --no-dependencies --out dist/e2e.vsix && node src/test/e2e/utils/build.js && playwright test",
+ "test:e2e": "playwright install && vsce package --no-dependencies --allow-package-secrets sendgrid --out dist/e2e.vsix && node src/test/e2e/utils/build.js && playwright test",
}
```

## 主要变更

### 🏗️ 重大架构重构

#### 1. 状态管理系统完全重构 (#5404)
- **核心变更**:
  - 删除 `src/core/storage/state.ts` (648 行代码)
  - 新增 `src/core/storage/state-keys.ts` (171 行) - 状态键定义
  - 新增 `src/core/storage/utils/state-helpers.ts` (442 行) - 状态辅助函数
  - 重构 `src/core/storage/CacheService.ts` (386 行变更) - 缓存服务优化
- **影响范围**:
  - 移除 `WorkspaceTracker` 依赖，简化工作区管理
  - 统一状态存储到缓存系统
  - 改进状态持久化和迁移机制
  - 增强状态访问的类型安全性

#### 2. 工具系统架构重构
- **工具文件整合**:
  - 删除 14 个独立工具文件 (总计约 800+ 行代码)
  - 工具逻辑整合到 `src/core/task/ToolExecutor.ts` (减少 300 行)
  - 简化 `src/core/task/index.ts` (减少 408 行)
- **新增多文件差异支持**:
  - 新增 `src/core/task/multifile-diff.ts` (121 行) - 多文件差异处理
  - 新增 `src/core/task/multifile-diff.test.ts` (303 行) - 相关测试
  - 新增 `src/hosts/vscode/hostbridge/diff/openMultiFileDiff.ts` - 多文件差异视图

#### 3. VSCode 状态 API 规范化
- **新增 ESLint 规则**:
  - 新增 `eslint-rules/no-direct-vscode-state-api.js` (144 行) - 禁止直接使用 VSCode 状态 API
  - 新增 `eslint-rules/__tests__/no-direct-vscode-state-api.test.ts` (171 行) - 规则测试
  - 更新 `.eslintrc.json` - 启用新规则
- **代码规范化**:
  - 移除所有直接的 VSCode 状态 API 调用
  - 统一通过缓存服务访问状态
  - 改进代码的可测试性和可维护性

### 🚀 新功能和模型支持

#### 1. OpenAI GPT-5 模型家族支持
- **新增模型**:
  - `gpt-5-chat-latest` - 最新 GPT-5 聊天模型
  - 最大令牌数: 8,192 (修复上下文窗口超限问题)
  - 完整的上下文窗口支持和错误处理
- **成本优化**:
  - 修复令牌计数问题，避免重复计费
  - 改进成本跟踪和使用统计

#### 2. Claude Sonnet 4 增强支持
- **上下文窗口扩展**:
  - 新增 1M 上下文窗口模型变体支持
  - 在模型信息中显示上下文窗口大小
  - 移除实验性 Sonnet 4 代码，使用稳定版本
- **SAP AI Core 集成**:
  - 校准 Anthropic 模型的输入令牌计数
  - 改进令牌使用统计的准确性

#### 3. Kimi 模型支持
- **新增模型**: `kimi-k2-turbo-preview`
- **完整集成**: 支持聊天、上下文处理和成本跟踪

### 🔧 系统优化和修复

#### 1. 浏览器集成增强 (#4871)
- **自定义浏览器参数**:
  - 支持向 Chrome 可执行文件传递自定义标志
  - 改进无头模式兼容性
  - 更好的浏览器会话管理
- **UI 改进**:
  - 浏览器设置菜单迁移到 Tailwind CSS
  - 改进设置界面的响应性和可访问性

#### 2. 文件上下文跟踪重构 (#5501)
- **迁移到 Chokidar**:
  - 从自定义文件监听器迁移到 `chokidar` 库
  - 改进文件变更检测的可靠性和性能
  - 更好的跨平台兼容性
- **测试增强**:
  - 新增 `FileContextTracker.test.ts` 测试覆盖
  - 改进文件监听逻辑的测试

#### 3. 终端环境变量支持 (#5367)
- **CLINE_ACTIVE 环境变量**:
  - 为新终端设置 `CLINE_ACTIVE=true` 环境变量
  - 改进终端会话识别和管理
  - 修复独立终端中 PATH 未设置的问题

#### 4. 认证和授权改进
- **OpenRouter 认证**:
  - 使用 HostProvider 创建 OpenRouter 认证 URL
  - 修复使用 OAuth 令牌而非刷新令牌调用使用端点的问题
  - 改进认证流程的可靠性
- **Requesty 配置修复**:
  - 修复 Requesty 的 API 密钥 URL
  - 更新提供商配置文档

### 🎨 用户界面和体验改进

#### 1. 聊天界面优化
- **操作按钮重构**:
  - 集中化操作按钮状态管理
  - 新增 `buttonConfig.ts` (297 行) - 按钮配置系统
  - 新增 `buttonConfig.test.ts` (147 行) - 按钮配置测试
  - 移除 `useButtonState.ts` 钩子，简化状态管理
- **计划模式改进**:
  - 禁用计划模式响应时的按钮
  - 改进用户交互反馈
  - 防止无任务时的操作按钮显示

#### 2. 文件导航增强
- **编辑器集成**:
  - 支持从聊天中的文件名点击跳转到编辑器
  - 改进文件上下文的可视化
  - 更好的多文件工作流程支持

#### 3. Tailwind CSS 集成
- **VSCode 主题颜色**:
  - 将 VSCode 主题颜色添加到 Tailwind 配置
  - 改进主题一致性和可定制性
  - 更好的深色/浅色模式支持

### 🧪 测试和开发体验

#### 1. 集成测试增强
- **新增测试端点**:
  - 新增 `proto/host/testing.proto` - 测试协议定义
  - 新增 `src/hosts/vscode/hostbridge/testing/getWebviewHtml.ts` - 测试支持
  - 改进端到端测试的可靠性

#### 2. 诊断系统重构
- **模块化诊断**:
  - 将 `FileDiagnostics` 从主机包移动到通用包
  - 新增 `src/integrations/diagnostics/__tests__/index.test.ts` (464 行测试)
  - 改进诊断功能的可测试性和重用性

#### 3. 代码质量改进
- **清理废弃代码**:
  - 移除实验性 Claude 4 代码
  - 移除废弃的 GPT-4.5 Preview 支持
  - 清理未使用的解析逻辑和工具函数
- **PostHog 优化**:
  - 移除 PostHog 异常自动捕获
  - 简化遥测服务实现
  - 改进隐私保护

## 详细文件列表

### 新增文件 (8 个)
1. **eslint-rules/no-direct-vscode-state-api.js** - VSCode 状态 API 使用规范
2. **eslint-rules/__tests__/no-direct-vscode-state-api.test.ts** - ESLint 规则测试
3. **proto/host/testing.proto** - 测试协议定义
4. **src/common.ts** - 通用工具函数
5. **src/core/storage/state-keys.ts** - 状态键定义
6. **src/core/storage/utils/state-helpers.ts** - 状态辅助函数
7. **src/core/task/multifile-diff.ts** - 多文件差异处理
8. **src/core/task/multifile-diff.test.ts** - 多文件差异测试

### 修改文件 (148 个)

#### 核心系统文件
1. **CHANGELOG.md** - 添加 v3.24.0 版本说明
2. **package.json** - 版本从 3.23.0 更新到 3.24.0，测试脚本优化
3. **package-lock.json** - 依赖版本同步更新
4. **.eslintrc.json** - 启用新的 VSCode 状态 API 规则
5. **.github/CODEOWNERS** - 代码所有者配置更新

#### API 和提供商 (11 个)
6. **src/api/index.ts** - API 索引更新，模型支持增强
7. **src/api/providers/anthropic.ts** - Anthropic 提供商优化
8. **src/api/providers/bedrock.ts** - Bedrock 提供商改进
9. **src/api/providers/cline.ts** - Cline 提供商重构
10. **src/api/providers/gemini.ts** - Gemini 提供商更新
11. **src/api/providers/litellm.ts** - LiteLLM 提供商修复
12. **src/api/providers/openrouter.ts** - OpenRouter 提供商增强
13. **src/api/providers/vertex.ts** - Vertex 提供商更新
14. **src/api/transform/openrouter-stream.ts** - OpenRouter 流处理
15. **docs/provider-config/openai-compatible.mdx** - OpenAI 兼容配置文档
16. **docs/provider-config/openai.mdx** - OpenAI 配置文档

#### 核心控制器系统 (35 个)
17. **src/core/controller/index.ts** - 控制器核心重构，移除 WorkspaceTracker
18. **src/core/controller/account/authStateChanged.ts** - 认证状态变更处理
19. **src/core/controller/account/openrouterAuthClicked.ts** - OpenRouter 认证处理
20. **src/core/controller/browser/*** (5 个文件) - 浏览器控制器更新
21. **src/core/controller/file/*** (12 个文件) - 文件控制器重构
22. **src/core/controller/models/*** (5 个文件) - 模型控制器更新
23. **src/core/controller/state/*** (9 个文件) - 状态控制器重构
24. **src/core/controller/task/*** (5 个文件) - 任务控制器优化
25. **src/core/controller/ui/*** (3 个文件) - UI 控制器改进

#### 核心任务和存储系统 (8 个)
26. **src/core/storage/CacheService.ts** - 缓存服务大幅重构
27. **src/core/storage/state-migrations.ts** - 状态迁移逻辑
28. **src/core/task/TaskState.ts** - 任务状态管理
29. **src/core/task/ToolExecutor.ts** - 工具执行器简化
30. **src/core/task/index.ts** - 任务核心逻辑重构
31. **src/core/assistant-message/index.ts** - 助手消息处理
32. **src/core/mentions/index.ts** - 提及功能优化
33. **src/core/prompts/system.ts** - 系统提示更新

#### 主机和集成系统 (15 个)
34. **src/extension.ts** - 扩展主文件重构，移除状态迁移
35. **src/hosts/external/*** (3 个文件) - 外部主机提供商
36. **src/hosts/vscode/*** (8 个文件) - VSCode 主机集成
37. **src/integrations/*** (4 个文件) - 集成服务优化

#### 服务和工具 (12 个)
38. **src/services/browser/*** (2 个文件) - 浏览器服务
39. **src/services/error/ClineError.ts** - 错误服务
40. **src/services/posthog/*** (2 个文件) - 遥测服务
41. **src/services/search/file-search.ts** - 文件搜索服务
42. **src/services/test/TestServer.ts** - 测试服务器
43. **src/shared/*** (3 个文件) - 共享组件
44. **src/standalone/*** (2 个文件) - 独立模式
45. **src/utils/*** (2 个文件) - 工具函数

#### WebView UI 组件 (25 个)
46. **webview-ui/src/components/browser/BrowserSettingsMenu.tsx** - 浏览器设置菜单
47. **webview-ui/src/components/chat/*** (8 个文件) - 聊天组件
48. **webview-ui/src/components/common/MarkdownBlock.tsx** - Markdown 块组件
49. **webview-ui/src/components/history/HistoryView.tsx** - 历史视图
50. **webview-ui/src/components/settings/*** (8 个文件) - 设置组件
51. **webview-ui/src/components/welcome/HomeHeader.tsx** - 欢迎页头部
52. **webview-ui/src/context/ExtensionStateContext.tsx** - 扩展状态上下文
53. **webview-ui/src/utils/context-mentions.ts** - 上下文提及工具
54. **webview-ui/tailwind.config.mjs** - Tailwind 配置

#### 协议和配置文件 (12 个)
55. **proto/cline/*** (5 个文件) - Cline 协议定义
56. **proto/host/*** (3 个文件) - 主机协议定义
57. **evals/diff-edits/ClineWrapper.ts** - 评估包装器
58. **src/test/*** (2 个文件) - 测试配置
59. **src/standalone/runtime-files/vscode/enhanced-terminal.js** - 增强终端
60. **docs/provider-config/requesty.mdx** - Requesty 配置文档

### 重点关注文件变更
1. **package.json** (6 行变更) - 版本从 3.23.0 更新到 3.24.0，测试脚本优化
2. **src/core/controller/index.ts** (160 行变更) - 移除 WorkspaceTracker，状态管理重构
3. **src/core/webview/index.ts** - 无直接变更，架构保持稳定
4. **src/extension.ts** (136 行变更) - 移除状态迁移逻辑，简化启动流程
5. **src/shared/ExtensionMessage.ts** - 无直接变更，消息协议保持稳定
6. **src/shared/WebviewMessage.ts** - 无直接变更，WebView 消息保持稳定
7. **webview-ui/src/context/ExtensionStateContext.tsx** (15 行变更) - 状态上下文优化

## 升级注意事项

### ✅ 兼容性说明
- **向后兼容**: 保持完全的向后兼容性，现有配置和数据不受影响
- **API 稳定性**: 核心 API 保持稳定，新增功能不影响现有集成
- **配置迁移**: 状态管理重构自动处理，无需手动迁移
- **扩展兼容**: 与现有 VSCode 扩展和工作流程完全兼容

### 🔧 新功能亮点
1. **GPT-5 模型支持**:
   - 最新 GPT-5 Chat 模型，8K 令牌限制
   - 改进的上下文窗口处理和错误恢复
2. **Claude Sonnet 4 增强**:
   - 1M 上下文窗口支持
   - 模型信息显示优化
3. **架构重构收益**:
   - 更稳定的状态管理
   - 简化的工具系统
   - 改进的代码质量和可维护性
4. **浏览器集成**:
   - 自定义浏览器参数支持
   - 更好的无头模式兼容性

### 📋 建议操作
1. **立即升级**: 强烈建议升级以获得架构改进和新模型支持
2. **体验新功能**:
   - 尝试 GPT-5 Chat 模型的改进性能
   - 体验 Claude Sonnet 4 的扩展上下文窗口
   - 测试改进的浏览器集成功能
3. **配置优化**:
   - 检查新的模型配置选项
   - 验证浏览器设置的改进
   - 体验更稳定的状态管理

### ⚠️ 重要提醒
- **状态管理**: 内部状态管理系统有重大重构，但用户体验无变化
- **工具系统**: 工具架构简化，功能保持不变但性能改进
- **ESLint 规则**: 新增代码规范检查，开发者需注意 VSCode 状态 API 使用
- **测试脚本**: E2E 测试脚本更新，CI/CD 流程可能需要调整

### 🔍 技术细节

#### 1. 状态管理重构影响
- **存储统一**: 所有状态通过 CacheService 统一管理
- **类型安全**: 新的状态键系统提供更好的类型检查
- **性能优化**: 减少状态访问开销，改进缓存策略
- **测试友好**: 状态管理更易于单元测试和集成测试

#### 2. 工具系统简化
- **代码减少**: 移除 800+ 行重复代码，整合到统一执行器
- **维护性**: 工具逻辑集中管理，减少维护成本
- **扩展性**: 新的架构更易于添加新工具和功能

#### 3. 多文件差异支持
- **新功能**: 支持同时查看和编辑多个文件的差异
- **UI 集成**: 与 VSCode 差异视图无缝集成
- **性能优化**: 高效的差异计算和渲染

---

**发布说明**: v3.24.0 是一个重要的架构重构版本，专注于代码质量、系统稳定性和新模型支持。这个版本显著简化了内部架构，同时保持了完全的向后兼容性和用户体验一致性。

**技术支持**: 如遇到升级相关问题，请参考相关文档或提交 Issue。特别注意新的状态管理系统和工具架构的改进。

**贡献者**: 感谢所有为此版本贡献代码、测试和反馈的开发者和用户！特别感谢对架构重构、新模型集成和代码质量改进的贡献。

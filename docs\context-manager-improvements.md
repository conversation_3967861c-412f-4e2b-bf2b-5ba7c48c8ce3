# ContextManager 改进方案

## 问题分析

### 1. 用户直接输入消息可能被截断

**现状问题**：
- 当前的 `getNextTruncationRange` 方法只保证保留第一个用户-助手对话对
- 其他用户直接输入的消息可能在上下文窗口管理时被截断
- 这会导致用户的重要指令、约束条件或背景信息丢失

**影响**：
- 用户提供的重要上下文信息可能丢失
- 助手可能忘记用户之前的指令和要求
- 对话连贯性受到影响

### 2. 文件重复检测只基于文件路径

**现状问题**：
- 当前实现使用 `filePath` 作为 key 来检测重复文件读取
- 同一个文件在不同时间点内容可能不同
- 这导致内容不同但路径相同的文件被错误地标记为重复

**影响**：
- 文件内容变化后的读取被错误地替换为"重复文件读取"提示
- 用户无法看到文件的实际变化
- 上下文信息不准确

## 改进方案

### 1. 保留所有用户直接输入消息

**实现思路**：
```typescript
// 识别用户直接输入（非工具调用结果）
private identifyUserDirectInputs(
    apiMessages: Anthropic.Messages.MessageParam[],
    startFromIndex: number
): Set<number> {
    const userDirectInputIndices = new Set<number>()
    
    for (let i = startFromIndex; i < apiMessages.length; i++) {
        const message = apiMessages[i]
        if (message.role === "user") {
            // 检查是否为直接用户输入（非工具调用结果）
            if (Array.isArray(message.content) && message.content.length > 0) {
                const firstBlock = message.content[0]
                if (firstBlock.type === "text") {
                    // 检查是否不是工具调用结果
                    const isToolResult = this.parsePotentialToolCall(firstBlock.text) !== null
                    if (!isToolResult) {
                        userDirectInputIndices.add(i)
                    }
                }
            }
        }
    }
    
    return userDirectInputIndices
}
```

**关键改进**：
- 在截断时识别并保护所有用户直接输入消息
- 优先截断工具调用和助手回复
- 当无法完全保护时，记录警告但确保系统稳定性

### 2. 基于 MD5 哈希的文件内容重复检测

**实现思路**：
```typescript
// 计算文件内容的 MD5 哈希
private calculateContentHash(content: string): string {
    return crypto.createHash('md5').update(content).digest('hex')
}

// 使用内容哈希作为重复检测的 key
private handleReadFileToolCall(
    i: number,
    filePath: string,
    fileReadIndices: Map<string, [number, number, string, string, string][]>,
    apiMessages: Anthropic.Messages.MessageParam[],
) {
    // 提取文件内容
    const message = apiMessages[i]
    let fileContent = ""
    
    if (message.role === "user" && Array.isArray(message.content) && message.content.length > 1) {
        const contentBlock = message.content[1]
        if (contentBlock.type === "text") {
            fileContent = contentBlock.text
        }
    }

    // 使用 MD5 哈希作为 key
    const contentHash = this.calculateContentHash(fileContent)
    const indices = fileReadIndices.get(contentHash) || []
    indices.push([i, EditType.READ_FILE_TOOL, "", formatResponse.duplicateFileReadNotice(), filePath])
    fileReadIndices.set(contentHash, indices)
}
```

**关键改进**：
- 使用文件内容的 MD5 哈希而不是文件路径作为重复检测的依据
- 同一文件的不同版本不会被错误地标记为重复
- 真正相同的内容（即使在不同时间点）会被正确识别为重复

## 数据结构变化

**原始结构**：
```typescript
Map<string, [number, number, string, string][]>
// key: filePath
// value: [messageIndex, EditType, searchText, replaceText]
```

**改进后结构**：
```typescript
Map<string, [number, number, string, string, string][]>
// key: contentHash (MD5)
// value: [messageIndex, EditType, searchText, replaceText, filePath]
```

## 测试策略

### 1. 用户输入保留测试
- 验证用户直接输入在截断时被保留
- 验证工具调用结果可以被正常截断
- 验证边界情况处理

### 2. 内容哈希重复检测测试
- 验证相同内容的文件被正确识别为重复
- 验证不同内容的同名文件不被标记为重复
- 验证文件提及（file mentions）的内容哈希处理

### 3. 集成测试
- 验证两个改进功能协同工作
- 验证性能影响在可接受范围内
- 验证向后兼容性

## 潜在风险和缓解措施

### 1. 性能影响
**风险**：MD5 计算可能影响性能
**缓解**：
- MD5 计算相对轻量
- 只对文件内容计算，频率不高
- 可考虑缓存机制

### 2. 内存使用
**风险**：保留更多用户消息可能增加内存使用
**缓解**：
- 只保留用户直接输入，不是所有消息
- 在极端情况下仍会回退到标准截断
- 监控内存使用情况

### 3. 向后兼容性
**风险**：数据结构变化可能影响现有功能
**缓解**：
- 保持公共接口不变
- 内部实现变化对外部透明
- 充分的测试覆盖

## 部署建议

1. **渐进式部署**：先在测试环境验证
2. **监控指标**：关注性能和内存使用
3. **回滚准备**：保留原始实现作为备选
4. **用户反馈**：收集用户对改进效果的反馈

## 总结

这些改进解决了 ContextManager 的两个关键问题：
1. **用户输入保护**：确保重要的用户指令不会在上下文管理中丢失
2. **精确重复检测**：基于内容而非路径的文件重复检测，提高准确性

改进后的系统将提供更好的用户体验和更准确的上下文管理。
